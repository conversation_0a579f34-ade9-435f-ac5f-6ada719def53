import pygame
import time
import json
from cars import Car, OpponentCar
from background import Background
from map import Map
from ui import TimeToStart, draw_end_screen, draw_profile_screen, draw_garage_screen, draw_shop_screen
from ui_components import TextButton
from save_ui import draw_save_menu
from save_system import save_system
from level_info import draw_level_info_screen, draw_level_complete_screen
from level_up_screen import draw_level_up_screen
from valuation_system import valuation_system
from selling_system import draw_garage_with_selling
from fuel_system import fuel_system
from tire_system import tire_system

class Game:
    def __init__(self):
        pygame.init()
        self.screen = pygame.display.set_mode((0, 0), pygame.FULLSCREEN)
        self.s_width, self.s_height = self.screen.get_size()
        pygame.display.set_caption('Xtreme Cars')
        self.clock = pygame.time.Clock()
        self.running = True

    def calculate_exp_required_for_level(self, level):
        """Calculate the total EXP required to reach a specific level using polynomial growth"""
        if level <= 1:
            return 0
        # Polynomial growth: base + (level^1.8 * 50) for more challenging progression
        # Level 2: ~141, Level 10: ~1581, Level 50: ~17678, Level 100: ~50000
        return int(100 + (level ** 1.8) * 50)

    def fix_level_progression(self, user_data):
        """Fix corrupted level progression data"""
        current_level = user_data['level']['current']
        current_exp = user_data['level']['exp']

        # Calculate what the required_to_next_level should be
        correct_required = self.calculate_exp_required_for_level(current_level + 1)

        # Check if player should have leveled up already
        while current_exp >= correct_required and correct_required > 0:
            current_level += 1
            correct_required = self.calculate_exp_required_for_level(current_level + 1)

        # Update the user data with corrected values
        user_data['level']['current'] = current_level
        user_data['level']['required_to_next_level'] = correct_required

        return user_data

    def calculate_level_up_rewards(self, new_level):
        """Calculate rewards for reaching a new level"""
        rewards = {
            'money': 0,
            'unlocked_parts': [],
            'unlocked_cars': [],
            'achievements': [],
            'special_bonuses': []
        }

        # Base money reward (scales with level)
        rewards['money'] = int(200 + (new_level * 150) + (new_level ** 1.5 * 25))

        # Unlock parts at specific levels
        part_unlocks = {
            5: [("engine", "Tuned I4")],
            10: [("turbo", "Small Turbo")],
            15: [("engine", "V6 Naturally Aspirated"), ("intercooler", "Large Front Mount")],
            20: [("ecu", "Stage 2 Tune")],
            25: [("turbo", "Medium Turbo")],
            30: [("engine", "V6 Twin Turbo"), ("intercooler", "Top Mount")],
            35: [("ecu", "Stage 3 Tune")],
            40: [("turbo", "Large Turbo"), ("intercooler", "Water-to-Air")],
            45: [("engine", "V8 Muscle")],
            50: [("turbo", "Ball Bearing Turbo"), ("ecu", "Custom Dyno Tune")],
            60: [("engine", "V8 High Performance"), ("intercooler", "Race Spec Intercooler")],
            70: [("turbo", "Variable Geometry Turbo"), ("ecu", "Race ECU")],
            80: [("engine", "V10 Supercar"), ("turbo", "Twin Turbo Setup")],
            90: [("ecu", "Pro Tuner ECU")],
            100: [("engine", "V12 Hypercar")]
        }

        if new_level in part_unlocks:
            rewards['unlocked_parts'] = part_unlocks[new_level]

        # Achievement milestones
        achievement_levels = {
            10: "Rookie Racer",
            25: "Street Veteran",
            50: "Racing Pro",
            75: "Speed Demon",
            100: "Racing Legend"
        }

        if new_level in achievement_levels:
            rewards['achievements'].append(achievement_levels[new_level])

        # Special bonuses for major milestones
        if new_level % 25 == 0:  # Every 25 levels
            bonus_money = new_level * 100
            rewards['special_bonuses'].append(f"Milestone Bonus: +{bonus_money}$")
            rewards['money'] += bonus_money

        return rewards

    def handle_level_up(self, player_exp, player_level, user_data):
        """Handle level progression correctly with rewards"""
        required_for_next = self.calculate_exp_required_for_level(player_level + 1)
        levels_gained = []
        total_rewards = {
            'money': 0,
            'unlocked_parts': [],
            'unlocked_cars': [],
            'achievements': [],
            'special_bonuses': []
        }

        # Keep leveling up until EXP is below the requirement
        while player_exp >= required_for_next:
            player_level += 1
            levels_gained.append(player_level)

            # Calculate rewards for this level
            level_rewards = self.calculate_level_up_rewards(player_level)

            # Accumulate rewards
            total_rewards['money'] += level_rewards['money']
            total_rewards['unlocked_parts'].extend(level_rewards['unlocked_parts'])
            total_rewards['unlocked_cars'].extend(level_rewards['unlocked_cars'])
            total_rewards['achievements'].extend(level_rewards['achievements'])
            total_rewards['special_bonuses'].extend(level_rewards['special_bonuses'])

            required_for_next = self.calculate_exp_required_for_level(player_level + 1)

        # Apply rewards to user data
        if levels_gained:
            user_data['money'] += total_rewards['money']

            # Add unlocked parts to inventory
            if 'inventory' not in user_data:
                user_data['inventory'] = {'owned_parts': {'engine': [], 'turbo': [], 'intercooler': [], 'ecu': []}}

            for part_type, part_name in total_rewards['unlocked_parts']:
                if part_type in user_data['inventory']['owned_parts']:
                    if part_name not in user_data['inventory']['owned_parts'][part_type]:
                        user_data['inventory']['owned_parts'][part_type].append(part_name)

        return player_level, required_for_next, levels_gained, total_rewards

    def run(self):
        self.main_menu()

    def main_menu(self):
        buttons = [
        TextButton('Single Player', self.s_width // 2 - 100, self.s_height // 2 - 250, action=self.single_player),
        TextButton('Wczytaj Grę', self.s_width // 2 - 100, self.s_height // 2 - 175, action=self.load_game_menu),
        TextButton('Zapisz Grę', self.s_width // 2 - 100, self.s_height // 2 - 100, action=self.save_game_menu),
        TextButton('Profil', self.s_width // 2 - 100, self.s_height // 2 - 25, action=lambda: draw_profile_screen(self.s_width, self.s_height, self.screen)),
        TextButton('Garaż', self.s_width // 2 - 100, self.s_height // 2 + 50, action=lambda: draw_garage_screen(self.s_width, self.s_height, self.screen)),
        TextButton('Sklep', self.s_width // 2 - 100, self.s_height // 2 + 125, action=lambda: draw_shop_screen(self.s_width, self.s_height, self.screen)),
        TextButton('Sprzedaż', self.s_width // 2 - 100, self.s_height // 2 + 200, action=lambda: draw_garage_with_selling(self.s_width, self.s_height, self.screen)),
        TextButton('Ustawienia', self.s_width // 2 - 100, self.s_height // 2 + 275, action=self.settings_menu),
        TextButton('Wyjdź z gry', self.s_width // 2 - 100, self.s_height // 2 + 350, action=pygame.quit)
    ]
        background = Background('background', self.s_width, self.s_height)
        while self.running:
            mouse_pos = pygame.mouse.get_pos()
            mouse_click = pygame.mouse.get_pressed()
            background.draw(self.screen)
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False

            for button in buttons:
                button.update(mouse_pos, mouse_click)
                button.draw(self.screen)

            pygame.display.update()

    def load_game_menu(self):
        """Show load game menu"""
        result = draw_save_menu(self.s_width, self.s_height, self.screen, mode="load")
        if result == "quit":
            self.running = False
        elif result == "loaded":
            # Game loaded successfully, show confirmation
            self.show_message("Gra została wczytana!")
        elif result == "new_game":
            # New game created
            self.show_message("Rozpoczęto nową grę!")

    def save_game_menu(self):
        """Show save game menu"""
        result = draw_save_menu(self.s_width, self.s_height, self.screen, mode="save")
        if result == "quit":
            self.running = False
        elif result == "saved":
            # Game saved successfully
            self.show_message("Gra została zapisana!")

    def settings_menu(self):
        """Placeholder for settings menu"""
        self.show_message("Menu ustawień - w przygotowaniu")

    def show_message(self, message):
        """Show a temporary message to the user"""
        font = pygame.font.SysFont("arial", 36)
        text = font.render(message, True, (255, 255, 255))

        # Show message for 2 seconds
        start_time = pygame.time.get_ticks()
        while pygame.time.get_ticks() - start_time < 2000:
            background = Background('background', self.s_width, self.s_height)
            background.draw(self.screen)

            # Center the message
            text_x = (self.s_width - text.get_width()) // 2
            text_y = (self.s_height - text.get_height()) // 2
            screen_rect = pygame.Rect(text_x - 20, text_y - 20, text.get_width() + 40, text.get_height() + 40)
            pygame.draw.rect(self.screen, (40, 40, 40), screen_rect)
            pygame.draw.rect(self.screen, (200, 200, 200), screen_rect, 2)
            self.screen.blit(text, (text_x, text_y))

            pygame.display.update()

            # Handle events to prevent freezing
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False
                    return

    def single_player(self):
        # Load user data first to get current level
        try:
            with open('data/profile.json') as f:
                user_data = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Error loading profile: {e}")
            self.show_message("Błąd wczytywania profilu!")
            return

        # Fix any corrupted level progression data
        user_data = self.fix_level_progression(user_data)

        # Save the fixed data back to profile
        with open('data/profile.json', 'w') as f:
            json.dump(user_data, f, indent=4)

        # Use race_level for opponent selection, default to player level if not set
        try:
            current_level = user_data.get('race_level', user_data["level"]['current'])
        except KeyError:
            print("Invalid profile data structure")
            self.show_message("Nieprawidłowe dane profilu!")
            return

        # Show level info screen
        level_info_result = draw_level_info_screen(self.s_width, self.s_height, self.screen, current_level)

        if level_info_result == "quit":
            self.running = False
            return
        elif level_info_result == "back":
            return
        elif level_info_result != "start_race":
            return

        # Now start the actual race
        bg = Map('map1', self.s_width, self.s_height)
        map_distance = bg.width * 2.5 - self.s_width
        distance_covered = 0
        is_map_ended = False

        try:
            with open('data/garage.json') as f:
                cars_data = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Error loading garage data: {e}")
            self.show_message("Błąd wczytywania danych garaży!")
            return

        try:
            with open('data/oponent_levels.json') as f:
                oponent_data = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Error loading opponent data: {e}")
            self.show_message("Błąd wczytywania danych przeciwników!")
            return

        # Validate selected car index
        selected_car_index = user_data["cars"].get("selected_car", 0)
        if selected_car_index < 0 or selected_car_index >= len(cars_data):
            print(f"Invalid selected car index: {selected_car_index}")
            self.show_message("Nieprawidłowy indeks samochodu!")
            return

        selected_car_data = cars_data[selected_car_index]

        # Ensure we don't exceed available levels
        max_level = len(oponent_data)
        if current_level > max_level:
            current_level = max_level

        opponent_data = oponent_data[current_level - 1]

        # Get the color for the selected car
        selected_car_index = user_data["cars"]["selected_car"]
        car_colors = user_data["cars"].get("car_colors", {})
        selected_color_index = car_colors.get(str(selected_car_index))

        # If no color set, use first available color
        if selected_color_index is None:
            available_colors = list(selected_car_data["color"].keys())
            selected_color_index = available_colors[0] if available_colors else "0"

        start_time = time.time()
        selected_car_index = user_data["cars"]["selected_car"]
        player = Car(
            selected_car_data["name"],
            selected_car_data["color"][selected_color_index],
            selected_car_data["weight"],
            selected_car_data["parts"],
            100, self.s_height // 5 * 3,
            start_time,
            selected_car_index  # Pass car index for condition tracking
        )
        opponent = OpponentCar(
            opponent_data["name"],
            opponent_data["color"]["0"],
            opponent_data["weight"],
            opponent_data["parts"],
            100, self.s_height // 5 * 3 + 100,
            start_time,
            opponent_data.get("opponent_name", f"Poziom {current_level}")
        )
        timer = TimeToStart()

        while self.running:
            dt = self.clock.tick(60) / 1000.0
            keys = pygame.key.get_pressed()
            is_map_ended = bg.is_map_ended(map_distance)

            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False

            self.screen.fill((20, 235, 35))
            bg.draw(self.screen)

            if timer.time > 0:
                timer.update_time()
                timer.draw(self.screen, self.s_width, self.s_height)
                player.update(None, None)
            else:
                player.update(keys, dt)
                opponent.update(dt)

                if not is_map_ended:
                    scroll_speed = player.speed * dt
                    bg.tick(scroll_speed)
                    distance_covered += scroll_speed
                    player.x_cord = 100
                else:
                    bg.x_cord = -map_distance

                player.draw(self.screen)
                opponent.draw(self.screen, distance_covered, is_map_ended, bg.x_cord)
                player_level = user_data['level']['current']
                player_exp = user_data['level']['exp']
                player_to_next_level = user_data['level']['required_to_next_level']
                player_money = user_data['money']
                if opponent.elapsed_time(map_distance):
                    # Player lost - but still get participation reward (rebalanced)
                    base_reward = 50 + (current_level * 30)  # Reduced participation reward

                    # Calculate performance bonus safely
                    opponent_time = opponent.elapsed_time(map_distance)
                    player_time = player.elapsed_time(map_distance)

                    if player_time is not None and player_time > 0:
                        # Player finished but lost - calculate based on time difference
                        performance_bonus = max(1.0, 1.5 - (opponent_time / player_time))
                    else:
                        # Player didn't finish - minimal bonus
                        performance_bonus = 1.0

                    reward = int(base_reward * performance_bonus)
                    # Significantly reduced XP for losing (more balanced)
                    exp_gain = int(reward * (0.1 + min(player_level, 50) / 200))  # Much lower XP gain, capped scaling
                    player_exp += exp_gain
                    player_money += reward

                    # Handle level progression correctly with rewards
                    old_player_level = player_level
                    player_level, player_to_next_level, levels_gained, level_rewards = self.handle_level_up(player_exp, player_level, user_data)

                    # Update money from level rewards
                    player_money = user_data['money']

                    # Update usage data after race
                    selected_car_index = user_data["cars"]["selected_car"]
                    if "usage_data" not in user_data:
                        user_data["usage_data"] = {"cars": {}}

                    if "cars" not in user_data["usage_data"]:
                        user_data["usage_data"]["cars"] = {}

                    if str(selected_car_index) not in user_data["usage_data"]["cars"]:
                        user_data["usage_data"]["cars"][str(selected_car_index)] = valuation_system.get_default_usage_data()

                    # Update usage data (lost race = 1 race completed)
                    car_usage = user_data["usage_data"]["cars"][str(selected_car_index)]
                    user_data["usage_data"]["cars"][str(selected_car_index)] = valuation_system.update_usage_data(car_usage, 1)

                    # Consume fuel based on race time
                    race_time = opponent.elapsed_time(map_distance)
                    if race_time and race_time > 0:
                        fuel_result = fuel_system.consume_fuel(selected_car_index, race_time, selected_car_data, car_usage)
                        # Apply tire wear based on race time
                        tire_result = tire_system.apply_tire_wear(selected_car_index, race_time, selected_car_data, "normal_driving")

                    # Update user_data dictionary with new values
                    user_data['level']['current'] = player_level
                    user_data['level']['exp'] = player_exp
                    user_data['level']['required_to_next_level'] = player_to_next_level
                    user_data['money'] = player_money

                    # Save updated user_data back to profile.json
                    with open('data/profile.json', 'w') as f:
                        json.dump(user_data, f, indent=4)

                    # Auto-save if current save slot is set
                    if save_system.current_save_slot:
                        save_system.save_game(save_system.current_save_slot)

                    # Show level-up screen if player leveled up
                    if levels_gained:
                        level_up_result = draw_level_up_screen(self.s_width, self.s_height, self.screen, levels_gained, level_rewards)
                        if level_up_result == "quit":
                            self.running = False
                            return

                    print(player_level)
                    return draw_end_screen(None, opponent.elapsed_time(map_distance), False, self.screen, self.s_width, reward, player_level)

                if player.elapsed_time(map_distance):
                    # Player won - advance to next level and give better rewards
                    old_level = current_level

                    # Calculate sophisticated reward system (rebalanced)
                    base_win_reward = 150 + (current_level * 100)  # Further reduced base reward
                    time_bonus = max(1.0, 1.8 - player.elapsed_time(map_distance) / 20.0)  # Further reduced time bonus
                    level_multiplier = 1.0 + (current_level * 0.12)  # Further reduced level multiplier

                    # Calculate opponent difficulty bonus
                    with open('data/oponent_levels.json') as f:
                        opponent_data = json.load(f)

                    if current_level <= len(opponent_data):
                        opponent_car = opponent_data[current_level - 1]
                        opponent_parts = opponent_car.get('parts', {})

                        # Calculate opponent car value for difficulty bonus
                        opponent_value = 0
                        for part_type, part_data in opponent_parts.items():
                            if part_data:
                                opponent_value += part_data.get('value', 0)

                        difficulty_bonus = 1.0 + (opponent_value / 10000)  # Bonus based on opponent car value
                    else:
                        difficulty_bonus = 1.0

                    reward = int(base_win_reward * time_bonus * level_multiplier * difficulty_bonus)
                    # Reduced XP for winning (more balanced progression)
                    exp_gain = int(reward * (0.2 + min(player_level, 50) / 100))  # Much more balanced, capped scaling
                    player_exp += exp_gain
                    player_money += reward

                    # Advance to next race level (different from player level)
                    max_race_level = len(oponent_data)
                    if current_level < max_race_level:
                        # Update the race level in profile
                        user_data['race_level'] = user_data.get('race_level', current_level) + 1

                    # Handle level progression correctly with rewards
                    old_player_level = player_level
                    player_level, player_to_next_level, levels_gained, level_rewards = self.handle_level_up(player_exp, player_level, user_data)

                    # Update money from level rewards
                    player_money = user_data['money']

                    # Update usage data after race
                    selected_car_index = user_data["cars"]["selected_car"]
                    if "usage_data" not in user_data:
                        user_data["usage_data"] = {"cars": {}}

                    if "cars" not in user_data["usage_data"]:
                        user_data["usage_data"]["cars"] = {}

                    if str(selected_car_index) not in user_data["usage_data"]["cars"]:
                        user_data["usage_data"]["cars"][str(selected_car_index)] = valuation_system.get_default_usage_data()

                    # Update usage data (won race = 1 race completed)
                    car_usage = user_data["usage_data"]["cars"][str(selected_car_index)]
                    user_data["usage_data"]["cars"][str(selected_car_index)] = valuation_system.update_usage_data(car_usage, 1)

                    # Consume fuel based on race time
                    race_time = player.elapsed_time(map_distance)
                    if race_time and race_time > 0:
                        fuel_result = fuel_system.consume_fuel(selected_car_index, race_time, selected_car_data, car_usage)
                        # Apply tire wear based on race time (more aggressive for winning)
                        tire_result = tire_system.apply_tire_wear(selected_car_index, race_time, selected_car_data, "aggressive_driving")

                    # Update user_data dictionary with new values
                    user_data['level']['current'] = player_level
                    user_data['level']['exp'] = player_exp
                    user_data['level']['required_to_next_level'] = player_to_next_level
                    user_data['money'] = player_money

                    # Save updated user_data back to profile.json
                    with open('data/profile.json', 'w') as f:
                        json.dump(user_data, f, indent=4)

                    # Auto-save if current save slot is set
                    if save_system.current_save_slot:
                        save_system.save_game(save_system.current_save_slot)

                    # Show level-up screen if player leveled up
                    if levels_gained:
                        level_up_result = draw_level_up_screen(self.s_width, self.s_height, self.screen, levels_gained, level_rewards)
                        if level_up_result == "quit":
                            self.running = False
                            return

                    # Show level complete screen
                    level_complete_result = draw_level_complete_screen(
                        self.s_width, self.s_height, self.screen,
                        old_level, reward, player_level
                    )

                    if level_complete_result == "quit":
                        self.running = False

                    return draw_end_screen(player.elapsed_time(map_distance), None, True, self.screen, self.s_width, reward, player_level)

            pygame.display.update()
