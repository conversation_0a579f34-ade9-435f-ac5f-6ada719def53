import pygame
from background import Background
from ui_components import <PERSON><PERSON><PERSON><PERSON>

def draw_level_up_screen(s_width, s_height, screen, levels_gained, rewards):
    """
    Display level-up celebration screen with rewards
    """
    if not levels_gained:
        return "continue"
    
    run = True
    bg = Background('background', s_width, s_height)
    
    # Create fonts
    header_font = pygame.font.SysFont("arial", 72, bold=True)
    title_font = pygame.font.SysFont("arial", 48, bold=True)
    info_font = pygame.font.SysFont("arial", 36)
    detail_font = pygame.font.SysFont("arial", 28)
    small_font = pygame.font.SysFont("arial", 24)
    
    # Create button
    continue_button = TextButton('Kontynuuj', s_width // 2 - 100, s_height - 150, font_size=36)
    
    # Animation variables
    start_time = pygame.time.get_ticks()
    celebration_duration = 3000  # 3 seconds of celebration
    
    # Colors
    gold_color = (255, 215, 0)
    silver_color = (192, 192, 192)
    green_color = (0, 255, 0)
    white_color = (255, 255, 255)
    yellow_color = (255, 255, 0)
    
    while run:
        current_time = pygame.time.get_ticks()
        elapsed_time = current_time - start_time
        
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return "quit"
            elif event.type == pygame.KEYDOWN or event.type == pygame.MOUSEBUTTONDOWN:
                if elapsed_time > 1000:  # Allow skip after 1 second
                    return "continue"
        
        # Update button
        mouse_pos = pygame.mouse.get_pos()
        mouse_click = pygame.mouse.get_pressed()
        continue_button.update(mouse_pos, mouse_click)

        # Check if button was clicked
        if continue_button.is_hovered and mouse_click[0]:
            return "continue"
        
        # Draw background
        bg.draw(screen)
        
        # Add celebration effect (pulsing background)
        if elapsed_time < celebration_duration:
            pulse_alpha = int(30 + 20 * abs(pygame.math.sin(elapsed_time * 0.01)))
            celebration_surface = pygame.Surface((s_width, s_height))
            celebration_surface.set_alpha(pulse_alpha)
            celebration_surface.fill(gold_color)
            screen.blit(celebration_surface, (0, 0))
        
        # Draw main header with animation
        if len(levels_gained) == 1:
            header_text = f"POZIOM {levels_gained[0]}!"
        else:
            header_text = f"POZIOMY {levels_gained[0]}-{levels_gained[-1]}!"
        
        # Animated header (grows in size)
        if elapsed_time < 1000:
            scale_factor = min(1.0, elapsed_time / 1000.0)
            font_size = int(72 * (0.5 + 0.5 * scale_factor))
            animated_header_font = pygame.font.SysFont("arial", font_size, bold=True)
            header_surface = animated_header_font.render(header_text, True, gold_color)
        else:
            header_surface = header_font.render(header_text, True, gold_color)
        
        header_x = (s_width - header_surface.get_width()) // 2
        screen.blit(header_surface, (header_x, 100))
        
        # Draw "LEVEL UP!" subtitle
        subtitle = title_font.render("AWANS!", True, green_color)
        subtitle_x = (s_width - subtitle.get_width()) // 2
        screen.blit(subtitle, (subtitle_x, 180))
        
        # Draw rewards section
        y_offset = 280
        
        if rewards['money'] > 0:
            money_text = info_font.render(f"💰 Bonus pieniędzy: +{rewards['money']}$", True, yellow_color)
            money_x = (s_width - money_text.get_width()) // 2
            screen.blit(money_text, (money_x, y_offset))
            y_offset += 50
        
        if rewards['unlocked_parts']:
            unlock_header = info_font.render("🔓 Odblokowane części:", True, white_color)
            unlock_x = (s_width - unlock_header.get_width()) // 2
            screen.blit(unlock_header, (unlock_x, y_offset))
            y_offset += 40
            
            for part_type, part_name in rewards['unlocked_parts']:
                part_text = detail_font.render(f"  • {part_name} ({part_type.upper()})", True, silver_color)
                part_x = (s_width - part_text.get_width()) // 2
                screen.blit(part_text, (part_x, y_offset))
                y_offset += 35
        
        if rewards['achievements']:
            achievement_header = info_font.render("🏆 Osiągnięcia:", True, white_color)
            achievement_x = (s_width - achievement_header.get_width()) // 2
            screen.blit(achievement_header, (achievement_x, y_offset))
            y_offset += 40
            
            for achievement in rewards['achievements']:
                achievement_text = detail_font.render(f"  • {achievement}", True, gold_color)
                achievement_x = (s_width - achievement_text.get_width()) // 2
                screen.blit(achievement_text, (achievement_x, y_offset))
                y_offset += 35
        
        if rewards['special_bonuses']:
            bonus_header = info_font.render("⭐ Specjalne bonusy:", True, white_color)
            bonus_x = (s_width - bonus_header.get_width()) // 2
            screen.blit(bonus_header, (bonus_x, y_offset))
            y_offset += 40
            
            for bonus in rewards['special_bonuses']:
                bonus_text = detail_font.render(f"  • {bonus}", True, gold_color)
                bonus_x = (s_width - bonus_text.get_width()) // 2
                screen.blit(bonus_text, (bonus_x, y_offset))
                y_offset += 35
        
        # Draw continue instruction (appears after celebration)
        if elapsed_time > celebration_duration:
            instruction_text = small_font.render("Kliknij lub naciśnij dowolny klawisz aby kontynuować", True, white_color)
            instruction_x = (s_width - instruction_text.get_width()) // 2
            screen.blit(instruction_text, (instruction_x, s_height - 200))
            
            # Draw button
            continue_button.draw(screen)
        
        pygame.display.update()
    
    return "continue"
