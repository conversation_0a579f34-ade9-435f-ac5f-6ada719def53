import json
import time
from valuation_system import valuation_system

class TireSystem:
    def __init__(self):
        # Tire types and their characteristics
        self.tire_types = {
            "economy": {
                "name": "Opony Ekonomiczne",
                "cost": 200,
                "durability": 100,  # Base durability points
                "grip_multiplier": 0.9,  # 90% grip
                "wear_rate": 1.0  # Standard wear rate
            },
            "standard": {
                "name": "Opony Standardowe", 
                "cost": 400,
                "durability": 120,
                "grip_multiplier": 1.0,  # 100% grip
                "wear_rate": 0.8  # 20% less wear
            },
            "performance": {
                "name": "Opony Sportowe",
                "cost": 800,
                "durability": 80,
                "grip_multiplier": 1.2,  # 120% grip
                "wear_rate": 1.5  # 50% more wear
            },
            "racing": {
                "name": "Opony Wyścigowe",
                "cost": 1500,
                "durability": 60,
                "grip_multiplier": 1.4,  # 140% grip
                "wear_rate": 2.0  # 100% more wear
            }
        }
        
        # Performance impact based on tire condition
        self.condition_performance_impact = {
            1.0: 1.0,   # 100% condition = full performance
            0.8: 0.95,  # 80% condition = 95% performance
            0.6: 0.85,  # 60% condition = 85% performance
            0.4: 0.70,  # 40% condition = 70% performance
            0.2: 0.50,  # 20% condition = 50% performance
            0.0: 0.30   # 0% condition = 30% performance
        }
        
        # Wear factors based on driving conditions
        self.wear_factors = {
            "aggressive_driving": 1.5,  # Hard acceleration/braking
            "normal_driving": 1.0,      # Standard driving
            "conservative_driving": 0.7, # Gentle driving
            "track_surface": 1.2,       # Racing on track
            "weather_dry": 1.0,         # Normal conditions
            "weather_wet": 0.8          # Less wear in wet conditions
        }
    
    def initialize_tire_data(self, car_index):
        """Initialize tire data for a car if not exists"""
        try:
            with open('data/profile.json', 'r') as f:
                profile_data = json.load(f)
            
            # Initialize tire data structure if not exists
            if "tire_data" not in profile_data:
                profile_data["tire_data"] = {"cars": {}}
            
            if "cars" not in profile_data["tire_data"]:
                profile_data["tire_data"]["cars"] = {}
            
            car_key = str(car_index)
            if car_key not in profile_data["tire_data"]["cars"]:
                # Start with standard tires
                profile_data["tire_data"]["cars"][car_key] = {
                    "tire_type": "standard",
                    "condition": 100.0,  # Start with new tires
                    "total_distance": 0.0,  # Total distance driven
                    "last_replacement": int(time.time())
                }
                
                # Save updated profile
                with open('data/profile.json', 'w') as f:
                    json.dump(profile_data, f, indent=4)
            
            return True
        except Exception as e:
            print(f"Error initializing tire data: {e}")
            return False
    
    def get_tire_data(self, car_index):
        """Get current tire data for a car"""
        try:
            with open('data/profile.json', 'r') as f:
                profile_data = json.load(f)
            
            car_key = str(car_index)
            tire_data = profile_data.get("tire_data", {}).get("cars", {}).get(car_key, {})
            
            if not tire_data:
                # Initialize if not exists
                self.initialize_tire_data(car_index)
                return self.get_tire_data(car_index)
            
            return tire_data
        except Exception as e:
            print(f"Error getting tire data: {e}")
            return {"tire_type": "standard", "condition": 100.0, "total_distance": 0.0, "last_replacement": int(time.time())}
    
    def calculate_tire_wear(self, car_data, race_time_seconds, driving_style="normal_driving"):
        """Calculate tire wear for a race"""
        # Get car performance data
        performance_data = valuation_system.calculate_enhanced_performance(car_data)
        
        # Base wear calculation
        base_wear = race_time_seconds * 0.5  # Base wear per second of racing
        
        # Modify based on car weight (heavier cars wear tires more)
        weight_factor = 1.0 + (performance_data["total_weight"] - 500) / 1000
        
        # Modify based on horsepower (more power = more wear)
        power_factor = 1.0 + (performance_data["total_horsepower"] - 200) / 500
        
        # Apply driving style factor
        style_factor = self.wear_factors.get(driving_style, 1.0)
        
        # Apply track surface factor
        surface_factor = self.wear_factors.get("track_surface", 1.2)
        
        total_wear = base_wear * weight_factor * power_factor * style_factor * surface_factor
        
        return max(0.1, total_wear)  # Minimum wear
    
    def apply_tire_wear(self, car_index, race_time_seconds, car_data, driving_style="normal_driving"):
        """Apply tire wear after a race"""
        try:
            tire_data = self.get_tire_data(car_index)
            tire_type = tire_data["tire_type"]
            current_condition = tire_data["condition"]
            
            # Calculate wear for this race
            wear_amount = self.calculate_tire_wear(car_data, race_time_seconds, driving_style)
            
            # Apply tire type wear rate modifier
            tire_info = self.tire_types.get(tire_type, self.tire_types["standard"])
            wear_rate_modifier = tire_info["wear_rate"]
            
            actual_wear = wear_amount * wear_rate_modifier
            new_condition = max(0.0, current_condition - actual_wear)
            
            # Update distance
            estimated_distance = race_time_seconds * 50  # Rough estimate: 50 units per second
            new_distance = tire_data["total_distance"] + estimated_distance
            
            # Update tire data
            with open('data/profile.json', 'r') as f:
                profile_data = json.load(f)
            
            car_key = str(car_index)
            profile_data["tire_data"]["cars"][car_key]["condition"] = new_condition
            profile_data["tire_data"]["cars"][car_key]["total_distance"] = new_distance
            
            with open('data/profile.json', 'w') as f:
                json.dump(profile_data, f, indent=4)
            
            # Auto-save if current save slot is set
            from save_system import save_system
            if save_system.current_save_slot:
                save_system.save_game(save_system.current_save_slot)
            
            return {
                "wear_applied": actual_wear,
                "new_condition": new_condition,
                "condition_percentage": new_condition,
                "performance_impact": self.get_performance_impact(new_condition / 100),
                "needs_replacement": new_condition < 20.0  # Recommend replacement below 20%
            }
            
        except Exception as e:
            print(f"Error applying tire wear: {e}")
            return {"wear_applied": 0, "new_condition": 100, "condition_percentage": 100, "performance_impact": 1.0, "needs_replacement": False}
    
    def get_performance_impact(self, condition_ratio):
        """Get performance impact based on tire condition"""
        for threshold, impact in sorted(self.condition_performance_impact.items(), reverse=True):
            if condition_ratio >= threshold:
                return impact
        return 0.30  # Minimum performance
    
    def replace_tires(self, car_index, tire_type="standard"):
        """Replace tires with new ones"""
        try:
            if tire_type not in self.tire_types:
                return False, "Nieprawidłowy typ opon"
            
            tire_info = self.tire_types[tire_type]
            cost = tire_info["cost"]
            
            # Check if player has enough money
            with open('data/profile.json', 'r') as f:
                profile_data = json.load(f)
            
            if profile_data["money"] < cost:
                return False, f"Nie masz wystarczająco pieniędzy. Potrzebujesz {cost}$"
            
            # Deduct money and replace tires
            profile_data["money"] -= cost
            car_key = str(car_index)
            
            # Initialize tire data if not exists
            if "tire_data" not in profile_data:
                profile_data["tire_data"] = {"cars": {}}
            if car_key not in profile_data["tire_data"]["cars"]:
                profile_data["tire_data"]["cars"][car_key] = {}
            
            profile_data["tire_data"]["cars"][car_key].update({
                "tire_type": tire_type,
                "condition": 100.0,  # New tires
                "total_distance": 0.0,  # Reset distance
                "last_replacement": int(time.time())
            })
            
            with open('data/profile.json', 'w') as f:
                json.dump(profile_data, f, indent=4)
            
            # Auto-save if current save slot is set
            from save_system import save_system
            if save_system.current_save_slot:
                save_system.save_game(save_system.current_save_slot)
            
            return True, f"Wymieniono opony na {tire_info['name']} za {cost}$"
            
        except Exception as e:
            print(f"Error replacing tires: {e}")
            return False, "Błąd podczas wymiany opon"
    
    def get_tire_info(self, car_index):
        """Get detailed tire information for display"""
        tire_data = self.get_tire_data(car_index)
        tire_type = tire_data["tire_type"]
        tire_info = self.tire_types.get(tire_type, self.tire_types["standard"])
        
        condition = tire_data["condition"]
        condition_category = self.get_condition_category(condition)
        performance_impact = self.get_performance_impact(condition / 100)
        
        return {
            "type_name": tire_info["name"],
            "type_key": tire_type,
            "condition": condition,
            "condition_category": condition_category,
            "performance_impact": performance_impact,
            "grip_multiplier": tire_info["grip_multiplier"] * performance_impact,
            "total_distance": tire_data["total_distance"],
            "needs_replacement": condition < 20.0,
            "replacement_cost": tire_info["cost"]
        }
    
    def get_condition_category(self, condition):
        """Get condition category string"""
        if condition >= 80:
            return "Doskonały"
        elif condition >= 60:
            return "Dobry"
        elif condition >= 40:
            return "Przeciętny"
        elif condition >= 20:
            return "Słaby"
        else:
            return "Krytyczny"

# Global tire system instance
tire_system = TireSystem()
