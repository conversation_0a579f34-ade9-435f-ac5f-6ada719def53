import json
import time
import random
from valuation_system import valuation_system

class MaintenanceSystem:
    def __init__(self):
        # Maintenance costs based on car performance class
        self.maintenance_costs = {
            "Economy": {"base_cost": 50, "multiplier": 1.0},
            "Standard": {"base_cost": 100, "multiplier": 1.2},
            "Performance": {"base_cost": 200, "multiplier": 1.5},
            "Sports Car": {"base_cost": 400, "multiplier": 2.0},
            "Supercar": {"base_cost": 800, "multiplier": 2.5},
            "Hypercar": {"base_cost": 1500, "multiplier": 3.0}
        }
        
        # Part-specific maintenance costs
        self.part_maintenance_costs = {
            "engine": {"base_cost": 200, "condition_multiplier": 2.0},
            "turbo": {"base_cost": 150, "condition_multiplier": 1.8},
            "intercooler": {"base_cost": 80, "condition_multiplier": 1.3},
            "ecu": {"base_cost": 100, "condition_multiplier": 1.1}
        }
        
        # Insurance costs and coverage
        self.insurance_plans = {
            "basic": {
                "name": "Ubezpieczenie Podstawowe",
                "monthly_cost": 100,
                "coverage": 0.5,  # Covers 50% of repair costs
                "max_payout": 5000
            },
            "standard": {
                "name": "Ubezpieczenie Standardowe",
                "monthly_cost": 250,
                "coverage": 0.75,  # Covers 75% of repair costs
                "max_payout": 15000
            },
            "premium": {
                "name": "Ubezpieczenie Premium",
                "monthly_cost": 500,
                "coverage": 0.9,  # Covers 90% of repair costs
                "max_payout": 50000
            }
        }
        
        # Crash damage types and severity
        self.crash_damage_types = {
            "minor": {"probability": 0.6, "damage_multiplier": 0.1, "name": "Drobne uszkodzenia"},
            "moderate": {"probability": 0.3, "damage_multiplier": 0.3, "name": "Umiarkowane uszkodzenia"},
            "major": {"probability": 0.08, "damage_multiplier": 0.6, "name": "Poważne uszkodzenia"},
            "total": {"probability": 0.02, "damage_multiplier": 1.0, "name": "Całkowite zniszczenie"}
        }
    
    def initialize_maintenance_data(self, car_index):
        """Initialize maintenance data for a car"""
        try:
            with open('data/profile.json', 'r') as f:
                profile_data = json.load(f)
            
            if "maintenance_data" not in profile_data:
                profile_data["maintenance_data"] = {"cars": {}, "insurance": None}
            
            if "cars" not in profile_data["maintenance_data"]:
                profile_data["maintenance_data"]["cars"] = {}
            
            car_key = str(car_index)
            if car_key not in profile_data["maintenance_data"]["cars"]:
                profile_data["maintenance_data"]["cars"][car_key] = {
                    "last_maintenance": int(time.time()),
                    "maintenance_due": False,
                    "total_maintenance_cost": 0,
                    "crashes": 0,
                    "repair_history": []
                }
                
                with open('data/profile.json', 'w') as f:
                    json.dump(profile_data, f, indent=4)
            
            return True
        except Exception as e:
            print(f"Error initializing maintenance data: {e}")
            return False
    
    def calculate_maintenance_cost(self, car_data, usage_data=None):
        """Calculate maintenance cost for a car"""
        if usage_data is None:
            usage_data = valuation_system.get_default_usage_data()
        
        # Get performance data
        performance_data = valuation_system.calculate_enhanced_performance(car_data, usage_data)
        performance_class = performance_data["performance_class"]
        
        # Base maintenance cost
        maintenance_info = self.maintenance_costs.get(performance_class, self.maintenance_costs["Standard"])
        base_cost = maintenance_info["base_cost"]
        multiplier = maintenance_info["multiplier"]
        
        # Adjust based on car condition
        avg_condition = performance_data["condition_effects"]["avg_parts_condition"]
        condition_multiplier = 2.0 - avg_condition  # Worse condition = higher cost
        
        # Adjust based on races completed
        races_completed = usage_data.get("races_completed", 0)
        usage_multiplier = 1.0 + (races_completed / 50)  # +2% per race
        
        total_cost = int(base_cost * multiplier * condition_multiplier * usage_multiplier)
        
        return {
            "total_cost": total_cost,
            "base_cost": base_cost,
            "performance_multiplier": multiplier,
            "condition_multiplier": condition_multiplier,
            "usage_multiplier": usage_multiplier
        }
    
    def is_maintenance_due(self, car_index):
        """Check if maintenance is due for a car"""
        try:
            with open('data/profile.json', 'r') as f:
                profile_data = json.load(f)
            
            # Get usage data
            usage_data = profile_data.get("usage_data", {}).get("cars", {}).get(str(car_index), {})
            races_completed = usage_data.get("races_completed", 0)
            
            # Get maintenance data
            maintenance_data = profile_data.get("maintenance_data", {}).get("cars", {}).get(str(car_index), {})
            last_maintenance_races = maintenance_data.get("last_maintenance_races", 0)
            
            # Maintenance due every 10 races
            return races_completed - last_maintenance_races >= 10
            
        except Exception as e:
            print(f"Error checking maintenance due: {e}")
            return False
    
    def perform_maintenance(self, car_index):
        """Perform maintenance on a car"""
        try:
            with open('data/garage.json', 'r') as f:
                garage_data = json.load(f)
            
            with open('data/profile.json', 'r') as f:
                profile_data = json.load(f)
            
            if car_index >= len(garage_data):
                return False, "Nieprawidłowy indeks samochodu"
            
            car_data = garage_data[car_index]
            usage_data = profile_data.get("usage_data", {}).get("cars", {}).get(str(car_index), {})
            
            # Calculate maintenance cost
            maintenance_cost_info = self.calculate_maintenance_cost(car_data, usage_data)
            total_cost = maintenance_cost_info["total_cost"]
            
            # Check if player has enough money
            if profile_data["money"] < total_cost:
                return False, f"Nie masz wystarczająco pieniędzy. Potrzebujesz {total_cost}$"
            
            # Deduct money
            profile_data["money"] -= total_cost
            
            # Initialize maintenance data if needed
            self.initialize_maintenance_data(car_index)
            
            # Update maintenance data
            car_key = str(car_index)
            current_races = usage_data.get("races_completed", 0)
            profile_data["maintenance_data"]["cars"][car_key].update({
                "last_maintenance": int(time.time()),
                "last_maintenance_races": current_races,
                "maintenance_due": False,
                "total_maintenance_cost": profile_data["maintenance_data"]["cars"][car_key].get("total_maintenance_cost", 0) + total_cost
            })
            
            # Improve part conditions slightly (maintenance benefit)
            if "usage_data" in profile_data and "cars" in profile_data["usage_data"]:
                if car_key in profile_data["usage_data"]["cars"]:
                    car_usage = profile_data["usage_data"]["cars"][car_key]
                    # Reduce age slightly (maintenance extends life)
                    for part_type in ["engine", "turbo", "intercooler", "ecu"]:
                        age_key = f"{part_type}_age_days"
                        if age_key in car_usage:
                            car_usage[age_key] = max(0, car_usage[age_key] - 2)  # Reduce age by 2 days
            
            with open('data/profile.json', 'w') as f:
                json.dump(profile_data, f, indent=4)
            
            # Auto-save if current save slot is set
            from save_system import save_system
            if save_system.current_save_slot:
                save_system.save_game(save_system.current_save_slot)
            
            return True, f"Konserwacja wykonana za {total_cost}$. Stan części został poprawiony."

        except Exception as e:
            print(f"Error performing maintenance: {e}")
            return False, "Błąd podczas konserwacji"

    def repair_car(self, car_index, repair_type="full"):
        """Perform comprehensive repair on a car to restore condition"""
        try:
            with open('data/profile.json', 'r') as f:
                profile_data = json.load(f)

            with open('data/garage.json', 'r') as f:
                garage_data = json.load(f)

            if car_index >= len(garage_data):
                return False, "Nieprawidłowy indeks samochodu"

            car_data = garage_data[car_index]
            car_key = str(car_index)

            # Get current usage data
            usage_data = profile_data.get("usage_data", {}).get("cars", {}).get(car_key,
                                                                                valuation_system.get_default_usage_data())

            # Calculate repair costs and benefits based on repair type
            if repair_type == "full":
                # Full restoration - expensive but restores to near-new condition
                base_cost = valuation_system.calculate_car_value(car_data, usage_data)["total_value"] * 0.3  # 30% of car value
                age_reduction = 0.7  # Reduce age by 70%
                description = "Pełna renowacja"

            elif repair_type == "major":
                # Major repair - moderate cost, good improvement
                base_cost = valuation_system.calculate_car_value(car_data, usage_data)["total_value"] * 0.15  # 15% of car value
                age_reduction = 0.4  # Reduce age by 40%
                description = "Główna naprawa"

            elif repair_type == "minor":
                # Minor repair - low cost, small improvement
                base_cost = valuation_system.calculate_car_value(car_data, usage_data)["total_value"] * 0.05  # 5% of car value
                age_reduction = 0.15  # Reduce age by 15%
                description = "Drobna naprawa"

            else:
                return False, "Nieprawidłowy typ naprawy"

            repair_cost = int(base_cost)

            # Check if player can afford it
            if profile_data['money'] < repair_cost:
                return False, f"Nie stać Cię na {description.lower()}. Koszt: {repair_cost} $"

            # Deduct money
            profile_data['money'] -= repair_cost

            # Apply repairs to usage data
            if "usage_data" not in profile_data:
                profile_data["usage_data"] = {"cars": {}}

            if "cars" not in profile_data["usage_data"]:
                profile_data["usage_data"]["cars"] = {}

            if car_key not in profile_data["usage_data"]["cars"]:
                profile_data["usage_data"]["cars"][car_key] = valuation_system.get_default_usage_data()

            car_usage = profile_data["usage_data"]["cars"][car_key]

            # Reduce age and wear for all components
            for part_type in ["car", "engine", "turbo", "intercooler", "ecu"]:
                age_key = f"{part_type}_age_days"
                if age_key in car_usage:
                    current_age = car_usage[age_key]
                    car_usage[age_key] = max(0, current_age * (1 - age_reduction))

            # Optionally reduce races completed slightly (represents refurbishment)
            if repair_type == "full":
                current_races = car_usage.get("races_completed", 0)
                car_usage["races_completed"] = max(0, int(current_races * 0.8))  # Reduce by 20%

            # Update repair history
            if "repair_data" not in profile_data:
                profile_data["repair_data"] = {"cars": {}}

            if "cars" not in profile_data["repair_data"]:
                profile_data["repair_data"]["cars"] = {}

            if car_key not in profile_data["repair_data"]["cars"]:
                profile_data["repair_data"]["cars"][car_key] = {"repairs": []}

            # Add repair record
            import time
            profile_data["repair_data"]["cars"][car_key]["repairs"].append({
                "type": repair_type,
                "cost": repair_cost,
                "timestamp": time.time(),
                "races_at_repair": car_usage.get("races_completed", 0)
            })

            with open('data/profile.json', 'w') as f:
                json.dump(profile_data, f, indent=4)

            # Auto-save if current save slot is set
            from save_system import save_system
            if save_system.current_save_slot:
                save_system.save_game(save_system.current_save_slot)

            return True, f"{description} wykonana za {repair_cost} $"

        except Exception as e:
            print(f"Error performing repair: {e}")
            return False, "Błąd podczas naprawy"

    def get_repair_options(self, car_index):
        """Get available repair options and their costs for a car"""
        try:
            with open('data/profile.json', 'r') as f:
                profile_data = json.load(f)

            with open('data/garage.json', 'r') as f:
                garage_data = json.load(f)

            if car_index >= len(garage_data):
                return []

            car_data = garage_data[car_index]
            car_key = str(car_index)

            # Get current usage data
            usage_data = profile_data.get("usage_data", {}).get("cars", {}).get(car_key,
                                                                                valuation_system.get_default_usage_data())

            car_value = valuation_system.calculate_car_value(car_data, usage_data)["total_value"]

            repair_options = [
                {
                    "type": "minor",
                    "name": "Drobna naprawa",
                    "description": "Podstawowa naprawa - przywraca 15% stanu",
                    "cost": int(car_value * 0.05),
                    "improvement": "15%"
                },
                {
                    "type": "major",
                    "name": "Główna naprawa",
                    "description": "Znacząca naprawa - przywraca 40% stanu",
                    "cost": int(car_value * 0.15),
                    "improvement": "40%"
                },
                {
                    "type": "full",
                    "name": "Pełna renowacja",
                    "description": "Kompletna renowacja - przywraca 70% stanu",
                    "cost": int(car_value * 0.3),
                    "improvement": "70%"
                }
            ]

            return repair_options

        except Exception as e:
            print(f"Error getting repair options: {e}")
            return []

    def simulate_crash_damage(self, car_index, car_data):
        """Simulate crash damage during a race"""
        # Determine crash severity
        rand = random.random()
        cumulative_prob = 0
        
        damage_type = "minor"
        for crash_type, info in self.crash_damage_types.items():
            cumulative_prob += info["probability"]
            if rand <= cumulative_prob:
                damage_type = crash_type
                break
        
        damage_info = self.crash_damage_types[damage_type]
        damage_multiplier = damage_info["damage_multiplier"]
        
        # Calculate repair cost based on car value
        car_valuation = valuation_system.calculate_car_value(car_data)
        base_repair_cost = int(car_valuation["total_value"] * damage_multiplier)
        
        # Apply insurance if available
        insurance_coverage = self.get_insurance_coverage(car_index)
        if insurance_coverage:
            covered_amount = min(
                int(base_repair_cost * insurance_coverage["coverage"]),
                insurance_coverage["max_payout"]
            )
            player_cost = base_repair_cost - covered_amount
        else:
            covered_amount = 0
            player_cost = base_repair_cost
        
        return {
            "damage_type": damage_type,
            "damage_name": damage_info["name"],
            "total_repair_cost": base_repair_cost,
            "insurance_coverage": covered_amount,
            "player_cost": player_cost,
            "damage_multiplier": damage_multiplier
        }
    
    def get_insurance_coverage(self, car_index):
        """Get current insurance coverage for a car"""
        try:
            with open('data/profile.json', 'r') as f:
                profile_data = json.load(f)
            
            insurance_plan = profile_data.get("maintenance_data", {}).get("insurance")
            if insurance_plan and insurance_plan in self.insurance_plans:
                return self.insurance_plans[insurance_plan]
            
            return None
        except Exception as e:
            print(f"Error getting insurance coverage: {e}")
            return None
    
    def purchase_insurance(self, plan_type):
        """Purchase insurance plan"""
        try:
            if plan_type not in self.insurance_plans:
                return False, "Nieprawidłowy plan ubezpieczeniowy"
            
            plan_info = self.insurance_plans[plan_type]
            monthly_cost = plan_info["monthly_cost"]
            
            with open('data/profile.json', 'r') as f:
                profile_data = json.load(f)
            
            if profile_data["money"] < monthly_cost:
                return False, f"Nie masz wystarczająco pieniędzy. Potrzebujesz {monthly_cost}$"
            
            # Deduct money and set insurance
            profile_data["money"] -= monthly_cost
            
            if "maintenance_data" not in profile_data:
                profile_data["maintenance_data"] = {"cars": {}, "insurance": None}
            
            profile_data["maintenance_data"]["insurance"] = plan_type
            profile_data["maintenance_data"]["insurance_purchased"] = int(time.time())
            
            with open('data/profile.json', 'w') as f:
                json.dump(profile_data, f, indent=4)
            
            # Auto-save if current save slot is set
            from save_system import save_system
            if save_system.current_save_slot:
                save_system.save_game(save_system.current_save_slot)
            
            return True, f"Wykupiono {plan_info['name']} za {monthly_cost}$"
            
        except Exception as e:
            print(f"Error purchasing insurance: {e}")
            return False, "Błąd podczas zakupu ubezpieczenia"

# Global maintenance system instance
maintenance_system = MaintenanceSystem()
