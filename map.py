import pygame

class Map:
    def __init__(self, name, screen_width, screen_height):
        self.x_cord = 0
        self.y_cord = 0
        self.img = pygame.image.load(f'./assets/img/maps/{name}.png')
        self.height = self.img.get_height() * screen_height // self.img.get_height()
        self.width = self.img.get_width() * screen_width // self.img.get_width()
        self.img_scaled = pygame.transform.scale(self.img, (self.width * 2.5, self.height))

    def tick(self, speed):
        self.x_cord -= speed

    def draw(self, screen):
        screen.blit(self.img_scaled, (self.x_cord, self.y_cord))

    def is_map_ended(self, map_distance):
        return -self.x_cord >= map_distance